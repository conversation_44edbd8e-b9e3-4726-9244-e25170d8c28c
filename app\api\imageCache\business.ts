import { sql } from "@vercel/postgres";
import { put } from '@vercel/blob';
import crypto from 'crypto';
import { ImageCacheModel } from "../../models/ImageCache";
import { ImageCacheTypeEnum } from "../../enums/ImageCacheType";
import { JC_Utils } from "../../Utils";
import { generateBase64FromUrl, compressImageToSize } from "../../utils/serverImageUtils";

// Replace ImageUrl with cached blob URL if available in database, store if not cached
export async function ReplaceCachedImageUrls<T extends { ImageUrl?: string }>(items: T[]): Promise<T[]> {
    if (!items || items.length === 0) {
        return items;
    }

    try {
        // Get all unique ImageUrls from the items (filter out undefined and empty strings)
        const imageUrls = [...new Set(items.map(item => item.ImageUrl).filter((url): url is string => url != null && url.trim() !== ''))];

        if (imageUrls.length === 0) {
            return items;
        }

        // Query database for cached URLs (prioritize Base64 over BlobUrl)
        const placeholders = imageUrls.map((_, index) => `$${index + 1}`).join(', ');
        const query = `
            SELECT "OriginalUrl", "BlobUrl", "Base64"
            FROM public."ImageCache"
            WHERE "OriginalUrl" IN (${placeholders})
        `;

        const result = await sql.query(query, imageUrls);
        const urlMap = new Map<string, string>();

        result.rows.forEach((row: any) => {
            // Prioritize Base64 over BlobUrl as requested
            if (row.Base64) {
                urlMap.set(row.OriginalUrl, `data:image/jpeg;base64,${row.Base64}`);
            } else if (row.BlobUrl) {
                urlMap.set(row.OriginalUrl, row.BlobUrl);
            }
        });

        // Find uncached URLs
        const uncachedUrls = imageUrls.filter(url => !urlMap.has(url));

        if (uncachedUrls.length === 0) {
            // All URLs are cached, just replace them
            return items.map(item => {
                if (item.ImageUrl && urlMap.has(item.ImageUrl)) {
                    return { ...item, ImageUrl: urlMap.get(item.ImageUrl) };
                }
                return item;
            });
        }

        // Store uncached images and add to urlMap
        for (const uncachedUrl of uncachedUrls) {
            try {
                const { blobUrl } = await StoreImage(uncachedUrl, ImageCacheTypeEnum.Base64);
                urlMap.set(uncachedUrl, blobUrl || uncachedUrl);
                console.log(`✅ Successfully created new cached image for URL: ${uncachedUrl.substring(0, 50)}...`);
            } catch (error) {
                console.warn(`❌ Failed to store image for URL: ${uncachedUrl}`, error);
                // Keep original URL if storage fails
                urlMap.set(uncachedUrl, uncachedUrl);
            }
        }

        // Replace ImageUrls with cached blob URLs
        return items.map(item => {
            if (item.ImageUrl && urlMap.has(item.ImageUrl)) {
                return { ...item, ImageUrl: urlMap.get(item.ImageUrl) };
            }
            return item;
        });
    } catch (error) {
        console.error('Error replacing cached image URLs:', error);
        return items; // Return original items on error
    }
}

export async function StoreImage(originalUrl: string, cacheType: ImageCacheTypeEnum): Promise<{ blobUrl: string }> {
    if (!originalUrl) {
        throw new Error('Missing url parameter');
    }

    try {
        // Check DB for existing mapping
        const existing = await sql<ImageCacheModel>`
            SELECT "Id", "OriginalUrl", "BlobUrl", "Base64", "CreatedAt"
            FROM public."ImageCache"
            WHERE "OriginalUrl" = ${originalUrl}
        `;

        if (existing.rows.length > 0) {
            console.log(`🎯 Found existing cached image for URL: ${originalUrl.substring(0, 50)}...`);

            if (cacheType === ImageCacheTypeEnum.Base64) {
                // If Base64 is requested but not stored, generate it
                if (!existing.rows[0].Base64) {
                    const base64Data = await generateBase64FromUrl(originalUrl, 50);

                    // Update the existing record with Base64 data
                    await sql`
                        UPDATE public."ImageCache"
                        SET "Base64" = ${base64Data}
                        WHERE "OriginalUrl" = ${originalUrl}
                    `;

                    return { blobUrl: `data:image/jpeg;base64,${base64Data}` };
                }
                return { blobUrl: `data:image/jpeg;base64,${existing.rows[0].Base64}` };
            } else {
                // Return BlobUrl for BlobStorage type
                return { blobUrl: existing.rows[0].BlobUrl };
            }
        }

        console.log(`🔄 Creating new cached image for URL: ${originalUrl.substring(0, 50)}...`);

        // Fetch image from original URL
        const imageRes = await fetch(originalUrl);
        if (!imageRes.ok) {
            throw new Error(`Failed to fetch image (${imageRes.status})`);
        }
        const buffer = Buffer.from(await imageRes.arrayBuffer());

        let blobUrl = '';
        let base64Data = '';

        if (cacheType === ImageCacheTypeEnum.BlobStorage) {
            // Create unique filename using generateGuid
            const guid = JC_Utils.generateGuid();
            const filename = `images/${guid}.jpg`;

            // Upload to Vercel Blob
            const { url } = await put(filename, buffer, {
                access: 'public',
                addRandomSuffix: false,
            });
            blobUrl = url;
        } else {
            // Generate Base64 with compression to keep under 50KB
            const compressedBuffer = await compressImageToSize(buffer, 50);
            base64Data = compressedBuffer.toString('base64');
            blobUrl = `data:image/jpeg;base64,${base64Data}`;
        }

        // Save mapping in DB
        await sql`
            INSERT INTO public."ImageCache" ("Id", "OriginalUrl", "BlobUrl", "Base64", "CreatedAt")
            VALUES (${crypto.randomUUID()}, ${originalUrl}, ${cacheType === ImageCacheTypeEnum.BlobStorage ? blobUrl : ''}, ${cacheType === ImageCacheTypeEnum.Base64 ? base64Data : ''}, ${new Date().toISOString()})
        `;

        console.log(`✅ Successfully created and stored new cached image with type: ${ImageCacheTypeEnum[cacheType]}`);
        return { blobUrl };
    } catch (error) {
        console.error('Error storing image:', error);
        throw new Error('Failed to store image');
    }
}


