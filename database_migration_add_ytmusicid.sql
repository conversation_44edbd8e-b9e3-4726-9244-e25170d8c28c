-- Migration script to add YtMusicId column to User table
-- This adds the new YtMusicId field that was added to the DBML schema

-- Add the YtMusicId column to the User table
ALTER TABLE public."User" 
ADD COLUMN "YtMusicId" varchar(100) NOT NULL DEFAULT '';

-- Optional: Add comment to document the new column
COMMENT ON COLUMN public."User"."YtMusicId" IS 'YouTube Music user identifier for API integration';

-- Note: You may want to update existing users with their actual YT Music IDs
-- Example update (uncomment and modify as needed):
-- UPDATE public."User" SET "YtMusicId" = 'actual_yt_music_id' WHERE "Id" = 'user_guid';
