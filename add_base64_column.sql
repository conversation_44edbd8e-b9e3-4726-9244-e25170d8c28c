-- SQL script to add Base64 column to ImageCache table
-- This assumes the table has already been renamed from ArtistImageCache to ImageCache
-- and GoogleUrl has been renamed to OriginalUrl

-- Add the Base64 column to the ImageCache table
ALTER TABLE public."ImageCache" 
ADD COLUMN "Base64" text NULL;

-- Optional: Add a comment to document the column
COMMENT ON COLUMN public."ImageCache"."Base64" IS 'Base64 encoded image data as alternative to BlobUrl';

-- Verification query (uncomment to run after adding column)
-- SELECT column_name, data_type, is_nullable 
-- FROM information_schema.columns 
-- WHERE table_name = 'ImageCache' 
-- AND table_schema = 'public'
-- ORDER BY ordinal_position;
