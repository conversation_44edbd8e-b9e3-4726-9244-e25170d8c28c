DESIGN PROMPTS FOR YT MUSIC AUTOMATOR APP - GOOGLE STITCH AI

=================================================================
OVERVIEW
=================================================================
Design three main pages for a YouTube Music automation web application called "YT Music Automator". The app helps users automatically manage their YouTube Music playlists by finding new songs from subscribed artists, adding entire artist catalogs, and adding the last played song to selected playlists.

=================================================================
COLOR SCHEME
=================================================================
Use the following color palette throughout all designs:

PRIMARY COLORS:
- Primary Color: #CC3189 (bright pink/magenta)
- Secondary Color: #ff0033 (bright red)
- Dark Primary: #7d124f (dark pink)
- Very Dark Primary: #47303d (very dark pink/brown)

ACCENT COLORS:
- Pastel Primary: #FD65BB (light pink)
- Pastel Secondary: #FF5778 (coral pink)
- Light Primary: #FFB1DD (very light pink)
- Light Secondary: #FF7E98 (light coral)
- Miscellaneous Color 1: #ef9c3c (orange)
- Miscellaneous Color 2: #70ad47 (green)

NEUTRAL COLORS:
- Off White: #F2F2F2 (background)
- Off Black: #303030 (text)
- Light Grey: #ececec
- Grey Hover: #dedede
- Error Color: #d54444 (red)

=================================================================
1. NEW SONGS PAGE DESIGN PROMPT
=================================================================

Design a "Get New Songs" page for a YouTube Music automation app with the following FUNCTIONALITY:

CORE PURPOSE:
- Automatically finds new releases (singles and albums) from artists the user subscribes to on YouTube Music
- Adds these new releases to a user-specified playlist
- Tracks which releases have already been processed to avoid duplicates

KEY FUNCTIONALITY:
- User enters their YouTube Music playlist URL where new songs should be added
- System fetches the user's YouTube Music subscriptions (artist channels they follow)
- For each subscribed artist, checks for new singles and albums released since last run
- Automatically adds new releases to the specified playlist
- Shows real-time progress of which artists are being processed
- Displays newly found singles and albums with cover art and titles
- All artist names and album/single titles link to their respective YouTube Music pages
- Limits functionality for non-signed-in users (only first 5 subscriptions)

INTERACTIVE ELEMENTS:
- Text input for playlist URL with validation
- "Run" button to start the automation process
- "Subscriptions" link to YouTube Music subscriptions page
- Loading states and progress indicators
- Clickable artist names, album covers, and song titles

=================================================================
2. ADD ALL ARTIST PAGE DESIGN PROMPT
=================================================================

Design an "Add All Artist" page for a YouTube Music automation tool with the following FUNCTIONALITY:

CORE PURPOSE:
- Allows users to add an entire artist's catalog (all singles and albums) to a specified playlist
- Fetches comprehensive artist data from YouTube Music including all releases
- Provides bulk playlist management with clear playlist option

KEY FUNCTIONALITY:
- User enters a YouTube Music artist URL to specify which artist's catalog to add
- User enters their playlist URL where the artist's songs should be added
- System validates both URLs and displays artist information and playlist name
- Shows large artist profile image when valid artist URL is entered
- "Clear Playlist" function to remove all songs from the destination playlist (limited to 100 songs at a time)
- Processes all artist singles first, then all albums
- Real-time progress display showing each single/album being added with cover art
- All album/single titles and covers link to their YouTube Music pages
- Limits functionality for non-signed-in users (only first 3 singles, no albums)

INTERACTIVE ELEMENTS:
- Text input for artist URL with real-time validation
- Text input for playlist URL with validation
- "Run" button to start adding all artist content
- "Clear Playlist" button with confirmation dialog
- Loading states during processing
- Clickable artist images, album covers, and titles
- "READY" status indicator when inputs are valid
- Progress tracking during the addition process

=================================================================
3. ADD LAST PAGE DESIGN PROMPT
=================================================================

Design an "Add Last Song" page for a YouTube Music automation app with the following FUNCTIONALITY:

CORE PURPOSE:
- Automatically detects the last song played in the user's YouTube Music history
- Adds that song to user-selected playlists from their collection
- Continuously monitors for new "last played" songs and updates in real-time

KEY FUNCTIONALITY:
- Automatically fetches and displays the most recently played song from YouTube Music history
- Shows large song artwork, title, and artist name for the current "last song"
- Updates every few seconds to detect when a new song becomes the "last played"
- Displays all user playlists in a selectable grid format
- Each playlist shows thumbnail, title, and track count
- Users can select multiple playlists to add the last song to
- "Run" button adds the current last song to all selected playlists
- Playlist management system with edit mode for hiding/showing playlists
- "All" and "None" buttons for bulk playlist selection
- Real-time feedback showing which playlists the song was successfully added to
- Link to YouTube Music history page for manual checking

INTERACTIVE ELEMENTS:
- "Run" button to execute adding last song to selected playlists
- Refresh button to manually reload playlist data
- History button linking to YouTube Music history
- Playlist checkboxes for selection
- Edit mode toggle with pencil icon
- Hide/show toggles for each playlist in edit mode
- "Save" and "Cancel" buttons for edit mode
- Clickable song artwork and title linking to YouTube Music track
- Loading states and progress indicators
- Authentication-dependent functionality (different features for signed-in vs guest users)

=================================================================
GENERAL DESIGN GUIDELINES FOR ALL PAGES
=================================================================

DESIGN APPROACH:
- Focus on functionality over specific layout constraints
- Emphasize the music automation theme with prominent album artwork and song imagery
- Create intuitive user interfaces that clearly communicate the automation processes
- Use the specified color scheme consistently across all pages
- Design for both desktop and mobile experiences
- Prioritize user feedback and loading states for all automation processes

VISUAL THEMES:
- Music-focused aesthetic with emphasis on album covers, artist images, and song artwork
- Modern, clean interface design
- Clear visual hierarchy for different types of content (inputs, progress, results)
- Consistent navigation and branding across all three pages

USER EXPERIENCE PRIORITIES:
- Clear indication of automation progress and status
- Intuitive form inputs with proper validation
- Real-time updates and feedback
- Easy playlist and content management
- Seamless integration feel with YouTube Music
- Appropriate limitations and messaging for different user authentication states

INTERACTIVE ELEMENTS:
- Prominent action buttons for starting automation processes
- Loading states and progress indicators
- Clickable links to YouTube Music content
- Form validation and error messaging
- Bulk selection and management tools
- Edit modes with clear save/cancel options
