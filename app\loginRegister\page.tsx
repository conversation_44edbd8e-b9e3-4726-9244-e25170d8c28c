"use client"

import styles from "./page.module.scss";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import JC_Form from "../components/JC_Form/JC_Form";
import JC_Tabs from "../components/JC_Tabs/JC_Tabs";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_Post } from "../apiServices/JC_Post";
import { useSearchParams } from 'next/navigation'
import { signIn, useSession } from "next-auth/react";
import { authenticate } from "../actions";
import { JC_Utils, JC_Utils_Validation } from "../Utils";
import { D_User, UserModel } from "../models/User";
import { D_FieldModel_Email, D_FieldModel_FirstName, D_FieldModel_LastName, D_FieldModel_Phone } from "../models/ComponentModels/JC_Field";
import { FieldTypeEnum } from "../enums/FieldType";
import { LocalStorageKeyEnum } from "../enums/LocalStorageKey";


export default function Page_LoginRegister() {

    const session = useSession();
    const params = useSearchParams();
    const router = useRouter();

    // - STATE - //

    // Loading
    const [isLoading, setIsLoading] = useState<boolean>(false);
    // Error
    const [errorMessage, setErrorMessage] = useState<string>();
    // Login
    const [loginEmail, setLoginEmail] = useState<string>("");
    const [loginPassword, setLoginPassword] = useState<string>("");


    // - INITIALISE - //

    useEffect(() => {
        if (!JC_Utils.isOnMobile()) {
            (document.getElementById("login-register-first-input") as HTMLInputElement).select();
        }
        if (localStorage.getItem(LocalStorageKeyEnum.JC_ShowForgotPasswordSent) == "1") {
            JC_Utils.showToastSuccess("A password reset link has been sent to your email!");
            localStorage.setItem(LocalStorageKeyEnum.JC_ShowForgotPasswordSent, "0");
        }
    }, []);


    // - HANDLES - //

    async function login() {
        setIsLoading(true);
        setErrorMessage("");
        // Login, then go back "Home"
        localStorage.setItem(LocalStorageKeyEnum.JC_ShowLoggedInWelcome, "1");
        let result = await authenticate(loginEmail, loginPassword);
        // IF error
        if (result.error) {
            setErrorMessage(result.error);
            setIsLoading(false);
        // ELSE sign in again so session updates properly then it takes User to Home
        } else {
            await signIn("credentials", { email: loginEmail, password: loginPassword, callbackUrl: "/" });
        }
    }


    // - Main - //

    return (
        <div className={styles.mainContainer}>
            {/* Form */}
            <JC_Form
                submitButtonText="Login"
                onSubmit={login}
                isLoading={isLoading}
                errorMessage={errorMessage}
                fields={[
                    // Email
                    {
                        ...D_FieldModel_Email(),
                        inputId:"login-register-first-input",
                        onChange: (newValue) => setLoginEmail(newValue),
                        value: loginEmail
                    },
                    // Password
                    {
                        inputId: "login-password-input",
                        type: FieldTypeEnum.Password,
                        label: "Password",
                        onChange: (newValue) => setLoginPassword(newValue),
                        value: loginPassword,
                        validate: (v:any) => JC_Utils.stringNullOrEmpty(v) ? "Enter a password." : ""
                    }
                ]}
            />
        </div>
    );
}
