import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/app/auth";
import { UpdateUserYtMusicTokens } from "../business";
import { JC_Utils_Security } from "@/app/Utils";

export async function POST(request: NextRequest) {
    try {
        // Get the current user session
        const session = await auth();
        if (!session?.user?.id) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        const { authToken, cookie } = await request.json();

        if (!authToken || !cookie) {
            return NextResponse.json({ error: "Auth token and cookie are required" }, { status: 400 });
        }

        // Encrypt the tokens before storing
        const encryptedAuthToken = JC_Utils_Security.encryptYtMusicToken(authToken);
        const encryptedCookie = JC_Utils_Security.encryptYtMusicToken(cookie);

        // Update the user's YT Music tokens
        await UpdateUserYtMusicTokens(session.user.id, encryptedAuthToken, encryptedCookie);

        return NextResponse.json({ status: 200, message: "YT Music tokens saved successfully" });

    } catch (error) {
        console.error("Error saving YT Music tokens:", error);
        return NextResponse.json({ error: "Failed to save YT Music tokens" }, { status: 500 });
    }
}
