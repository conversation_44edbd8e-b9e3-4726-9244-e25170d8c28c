import { NextRequest, NextResponse } from "next/server";
import { GetUser, UpdateUserPassword } from "../business";
import { UserModel } from "@/app/models/User";
import { JC_Utils_Security } from "@/app/Utils";

export async function POST(request: NextRequest) {
    try {

        const { userId, newPassword } = await request.json();

        // First check if entered current password matches User's password
        // let user:UserModel = await GetUser(userId);
        // if (!(await JC_Utils_Security.comparePassword(currentPassword, user.PasswordHash))) {
        //     return NextResponse.json({ status: 500, error: "The password you entered for \"Current Password\" does not match your current password." });
        // }

        await UpdateUserPassword(userId, await JC_Utils_Security.hashPassword(newPassword));

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}