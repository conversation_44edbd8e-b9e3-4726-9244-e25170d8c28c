@import '../global';

.mainContainer {
    @include mainPageStyles;
    margin: 30px 70px;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    row-gap: 20px;

    // Small Text
    .smallTextButton {
        font-weight: bold;
        color: $offWhite;
        user-select: none;
        cursor: pointer;

        &:hover {
            color: $secondaryColor;
        }
    }

    // Password Requirements
    .passwordRequirementsText {
        position: absolute;
        left: calc(50% + 220px);
        top: 424px;
        width: max-content;
        > ul {
            margin-top: 4px;
        }
    }

}


// - SCREEN SIZES - //

@media (max-width: $smallScreenSize) {
    .passwordRequirementsText {
        position: inherit !important;
        left: 0 !important;
        top: 0 !important;
    }
}