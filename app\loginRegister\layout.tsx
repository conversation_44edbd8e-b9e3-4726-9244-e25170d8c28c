import type { <PERSON>ada<PERSON> } from "next";
import { redirect } from 'next/navigation'
import { auth } from '../auth';
import React from "react";
import JC_Header from "../components/JC_Header/JC_Header";

// Site Metadata
export const metadata: Metadata = {
    title: "YT Music Automator - Forgot Password",
    description: "Change your password."
};

export default async function Layout_LoginRegister(_: Readonly<{
    children: React.ReactNode;
}>) {

    // - AUTH - //

    // const session = await auth();
    // if (session) {
    //     redirect("/newSongs");
    // }


    // - MAIN - //

    return <React.Fragment>
        <JC_Header />
        {_.children}
    </React.Fragment>;

}
