-- Migration script to update UserProcessProgress table
-- Add new columns: <PERSON><PERSON><PERSON>, ItemsCompleted<PERSON>son, ItemsLeft<PERSON>son

-- Add the new columns to the UserProcessProgress table
ALTER TABLE public."UserProcessProgress" 
ADD COLUMN "DataJson" varchar NULL,
ADD COLUMN "ItemsCompleted<PERSON>son" varchar NULL,
ADD COLUMN "ItemsLeftJson" varchar NULL;

-- Optional: Add comments to document the new columns
COMMENT ON COLUMN public."UserProcessProgress"."Data<PERSON>son" IS 'JSON data for general process information';
COMMENT ON COLUMN public."UserProcessProgress"."ItemsCompletedJson" IS 'JSON array of completed items (ArtistItemModel[] or UserArtistSubscriptionModel[])';
COMMENT ON COLUMN public."UserProcessProgress"."ItemsLeftJson" IS 'JSON array of remaining items to process (ArtistItemModel[] or UserArtistSubscriptionModel[])';

-- Note: The existing ProgressDataJson column is kept for backward compatibility
-- but will now contain only the core progress data without the item lists
